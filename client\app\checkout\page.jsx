"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  ChevronDown,
  ChevronUp,
  Trash2,
  ArrowLeft,
  ShoppingCart,
  CreditCard,
  Shield,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useCart } from "@/context/cart-context";
import { useAuth } from "@/context/auth-context";
import { useCheckout } from "@/context/checkout-context";
import CartModal from "@/components/cart-modal";
import AuthModal from "@/components/auth-modal";
import Footer from "@/components/footer";
import MobileNav from "@/components/mobile-nav";
import { motion, AnimatePresence } from "framer-motion";
import Navbar from "@/components/navbar";
import { useMediaQuery } from "@/hooks/use-media-query";

import { toast } from "sonner";

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isCartOpen, toggleCart } = useCart();
  const { user } = useAuth();
  const {
    checkoutData,
    loading,
    initializeCheckout,
    processPayment,
    validateCheckout,
    currentOrder,
    createTicketsAfterPayment,
  } = useCheckout();

  const [expandedSegments, setExpandedSegments] = useState({});
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState("login");

  const isMobile = useMediaQuery("(max-width: 768px)");

  // Get eventId from URL parameters
  const eventId = searchParams.get("eventId");

  // Auth modal helper function
  const openAuthModal = (mode) => {
    setAuthMode(mode);
    setShowAuthModal(true);
  };

  // Initialize checkout when component mounts
  useEffect(() => {
    if (user) {
      initializeCheckout(eventId);
      console.log(localStorage);
    }
  }, [user, eventId]);

  // Redirect if not logged in
  if (!user) {
    openAuthModal("login");
    return null;
  }

  // Show loading while initializing checkout
  if (loading) {
    return (
      <div className="min-h-screen bg-background text-text-primary flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-lg">Loading checkout...</p>
        </div>
      </div>
    );
  }

  // Redirect if no checkout data available
  // if (!checkoutData || !checkoutData.items || checkoutData.items.length === 0) {
  //   router.push("/events");
  //   return null;
  // }

  // Group checkout items by event (segments)
  const segments = checkoutData
    ? checkoutData.items.reduce((groups, item, index) => {
        const itemEventId = item.eventId;
        if (!groups[itemEventId]) {
          groups[itemEventId] = {
            eventTitle: item.eventTitle,
            eventDate: item.eventDate,
            eventVenue: item.eventVenue || "TBA",
            items: [],
            subtotal: 0,
          };
        }
        groups[itemEventId].items.push({ ...item, originalIndex: index });
        groups[itemEventId].subtotal += item.price * item.quantity;
        return groups;
      }, {})
    : {};

  const toggleSegment = (eventId) => {
    setExpandedSegments((prev) => ({
      ...prev,
      [eventId]: !prev[eventId],
    }));
  };

  const handleRemoveItem = async (orderItemId) => {
    // This would need to call an API to remove the order item
    // For now, we'll just show a toast
    toast.info(
      `Item removal will be implemented for order item ${orderItemId}`
    );
  };

  console.log(checkoutData);
  // Calculate totals from checkout data
  const subtotal = checkoutData ? checkoutData.total : 0;
  const totalAmount = subtotal;

  const handleProceedToPay = async () => {
    try {
      if (!user) {
        toast.error("Please login to continue");
        return;
      }

      const validation = validateCheckout();
      if (!validation.valid) {
        toast.error(validation.message);
        return;
      }

      // Use the processPayment function from checkout context
      const result = await processPayment();

      if (!result.success) {
        toast.error(result.message || "Payment failed. Please try again.");
      }

      createTicketsAfterPayment(currentOrder);
      toast.success("Payment initiated. Please complete the transaction.");

      
    } catch (error) {
      console.error("Payment processing error:", error);
      toast.error(error.message || "Payment failed. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-background text-text-primary">
      <Navbar
        onLoginClick={() => openAuthModal("login")}
        onRegisterClick={() => openAuthModal("register")}
      />

      {/* Header */}
      <main className="pb-20 md:pb-8 pt-20">
        <div className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4">
            <div className="flex flex-row justify-items-center h-16">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="text-text-secondary hover:text-text-primary"
              >
                <ArrowLeft className="h-6 w-6" />
              </Button>
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5 text-primary-600" />
                <h1 className="text-4xl font-bold">Checkout</h1>
              </div>
            </div>
            <div className="text-sm text-text-secondary">
              {checkoutData
                ? checkoutData.items.reduce(
                    (count, item) => count + item.quantity,
                    0
                  )
                : 0}{" "}
              items
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Section - Tickets by Segments */}
            <div className="lg:col-span-2">
              <div className="bg-background-50 rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-6 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-primary-600" />
                  Your Tickets
                </h2>

                <div className="space-y-4">
                  {Object.entries(segments).map(([eventId, segment]) => (
                    <div
                      key={eventId}
                      className="border border-background-200 rounded-lg overflow-hidden"
                    >
                      {/* Segment Header */}
                      <div
                        className="p-4 bg-background-100 cursor-pointer hover:bg-background-200 transition-colors"
                        onClick={() => toggleSegment(eventId)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg">
                              {segment.eventTitle}
                            </h3>
                            <div className="flex items-center space-x-4 mt-1 text-sm text-text-muted">
                              <span>
                                {new Date(
                                  segment.eventDate
                                ).toLocaleDateString()}
                              </span>
                              <span>•</span>
                              <span>{segment.eventVenue}</span>
                              <span>•</span>
                              <span>
                                {segment.items.length} ticket
                                {segment.items.length > 1 ? "s" : ""}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <span className="font-semibold">
                              ${segment.subtotal.toFixed(2)}
                            </span>
                            {expandedSegments[eventId] ? (
                              <ChevronUp className="h-5 w-5 text-text-muted" />
                            ) : (
                              <ChevronDown className="h-5 w-5 text-text-muted" />
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Segment Content */}
                      <AnimatePresence>
                        {expandedSegments[eventId] && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="overflow-hidden"
                          >
                            <div className="p-4 space-y-3 bg-background-100">
                              {segment.items.map((item, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between p-3 bg-background-50 rounded-lg"
                                >
                                  <div className="flex-1">
                                    <div className="font-medium">
                                      {item.ticketType}
                                    </div>
                                    <div className="text-sm text-text-muted">
                                      ${item.price.toFixed(2)} × {item.quantity}
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-3">
                                    <span className="font-semibold">
                                      ${(item.price * item.quantity).toFixed(2)}
                                    </span>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        handleRemoveItem(item.order_item_id)
                                      }
                                      className="text-primary-600 hover:text-primary-700 hover:bg-primary-600/10"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Section - Payment Summary */}
            <div className="lg:col-span-1">
              <div className="bg-background-50 rounded-lg p-6 sticky top-24">
                <h2 className="text-xl font-semibold mb-6">Order Summary</h2>

                {/* Price Breakdown */}
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between text-text-secondary">
                    <span>Subtotal</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>

                  <div className="border-t border-background-200 pt-3">
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total Payable</span>
                      <span className="text-primary-600">
                        ${totalAmount.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Proceed to Pay Button */}
                <Button
                  onClick={handleProceedToPay}
                  disabled={
                    loading ||
                    !checkoutData ||
                    !checkoutData.items ||
                    checkoutData.items.length === 0
                  }
                  className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 mb-6 disabled:opacity-50 disabled:cursor-not-allowed"
                  size="lg"
                >
                  {loading
                    ? "Processing..."
                    : `Proceed to Pay $${totalAmount.toFixed(2)}`}
                </Button>

                {/* Disclaimer */}
                <div className="space-y-4 text-xs text-text-muted">
                  <div className="flex items-start space-x-2">
                    <Shield className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-text-secondary mb-1">
                        Secure Payment
                      </p>
                      <p>
                        Your payment information is encrypted and secure. We use
                        industry-standard SSL encryption.
                      </p>
                    </div>
                  </div>

                  <div className="border-t border-background-200 pt-4">
                    <p className="font-medium text-text-secondary mb-2">
                      Terms & Conditions
                    </p>
                    <ul className="space-y-1">
                      <li>• All ticket sales are final and non-refundable</li>
                      <li>
                        • Tickets are non-transferable unless specified by the
                        organizer
                      </li>
                      <li>
                        • Event details are subject to change by the organizer
                      </li>
                      <li>
                        • You must present valid ID matching the ticket holder's
                        name
                      </li>
                    </ul>
                  </div>

                  <div className="border-t border-background-200 pt-4">
                    <p className="text-center">
                      By proceeding with payment, you agree to our{" "}
                      <button className="text-primary-600 hover:text-primary-700 underline">
                        Terms of Service
                      </button>{" "}
                      and{" "}
                      <button className="text-primary-600 hover:text-primary-700 underline">
                        Privacy Policy
                      </button>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {isMobile && <MobileNav />}

      {isCartOpen && <CartModal />}

      {showAuthModal && (
        <AuthModal
          mode={authMode}
          onClose={() => setShowAuthModal(false)}
          onSwitchMode={() =>
            setAuthMode(authMode === "login" ? "register" : "login")
          }
        />
      )}
    </div>
  );
}
