# Event-Based Order Creation Implementation

## Overview
This document describes the implementation of event-based order creation logic in the ticketing system. The system now prevents duplicate pending orders for the same user-event combination while allowing multiple completed orders.

## Business Logic Requirements

### Conditional Order Creation Rules
1. **Case 1**: If no order exists for user-event combination → Create new order
2. **Case 2**: If pending order exists for user-event combination → Add to existing order
3. **Case 3**: If only completed orders exist for user-event combination → Create new order

## Implementation Details

### New Helper Methods

#### `findUserEventOrder(userId, eventId, paymentStatus)`
- Finds existing orders for a specific user-event combination with given payment status
- Uses Prisma query with nested relations to check orderitems → tickettypes → event_id
- Returns the order with included orderitems and tickettypes, or null if not found

#### `getEventIdFromItems(items)`
- Extracts and validates event ID from cart items or selected tickets
- Ensures all items are from the same event (throws error if multiple events)
- Handles both cart items (with tickettypes relation) and selected tickets (with ticketTypeId)

#### `addItemsToExistingOrder(existingOrder, cartItems)`
- Adds cart items to an existing pending order
- Updates quantities for existing ticket types
- Creates new order items for new ticket types
- Recalculates order total and additional fees

#### `createNewOrderFromItems(userId, cartItems)`
- Creates a new order from cart items
- Calculates total amount and additional fees (5% organizer + 10% service)
- Creates order with pending status
- Creates all order items

#### `addTicketsToExistingOrder(existingOrder, selectedTickets)`
- Similar to addItemsToExistingOrder but for selected tickets format
- Handles ticket objects with ticketTypeId, quantity, and price

#### `createNewOrderFromTickets(userId, selectedTickets)`
- Creates new order from selected tickets format
- Same fee calculation and order creation logic

#### `updateOrderTotal(orderId)`
- Helper method to recalculate and update order totals
- Fetches all order items, calculates new total, and updates order

### Modified Methods

#### `createOrderFromCart(userId, cartItems)`
**Before**: Always created new order regardless of existing orders
**After**: 
- Validates cart items are from same event
- Checks for existing pending order for user-event combination
- Either adds to existing order or creates new order based on business rules

#### `createOrderFromTickets(userId, selectedTickets, ticketsWithAttendeeInfo)`
**Before**: Always created new order regardless of existing orders
**After**:
- Validates selected tickets are from same event
- Checks for existing pending order for user-event combination
- Either adds to existing order or creates new order based on business rules
- Preserves attendee information in response

#### `addToCart(userId, ticketTypeId, quantity)`
**Before**: Checked for pending orders by ticket type only
**After**:
- Gets event ID from ticket type
- Checks for existing pending order for user-event combination
- Either adds to existing order or creates new order based on business rules
- Maintains all existing validation (availability, max per order, etc.)

## Database Schema Relationships

The implementation relies on these key relationships:
```
orders → orderitems → tickettypes → events
```

This allows querying orders by event ID through the nested relationships.

## Fee Calculation

All order creation methods now consistently apply:
- **Organizer Fees**: 5% of total amount
- **Service Fees**: 10% of total amount
- **Total Additional Fees**: 15% of total amount

## Error Handling

- Validates items are from the same event
- Maintains existing ticket availability and max per order validations
- Proper error messages for all failure scenarios
- Database transaction safety

## Testing

### Manual Test Results
✅ Event ID extraction from items
✅ User-event order finding logic
✅ Conditional logic scenarios identification
✅ Database relationship validation
✅ Fee calculation verification

### Test Scenarios Covered
1. **No existing orders** → Creates new order
2. **Existing pending order** → Adds to existing order
3. **Only completed orders** → Creates new order
4. **Multiple ticket types** → Handles quantity updates and new items
5. **Fee calculations** → Consistent 15% additional fees

## API Response Changes

### New Response Fields
- `isExistingOrder`: Boolean indicating if items were added to existing order vs new order created

### Backward Compatibility
All existing API endpoints maintain their current response structure with additional fields.

## Benefits

1. **Prevents Duplicate Pending Orders**: Users can't accidentally create multiple pending orders for the same event
2. **Improved User Experience**: Cart items automatically consolidate into single order per event
3. **Consistent Fee Calculation**: All order creation paths now apply the same fee structure
4. **Maintains Data Integrity**: Proper validation ensures all items in an order are from the same event
5. **Flexible Order Management**: Allows multiple completed orders while preventing pending duplicates

## Migration Notes

- No database schema changes required
- Existing orders and order items remain unchanged
- New logic only affects future order creation
- All existing API endpoints continue to work

## Future Enhancements

1. Add order consolidation for cross-event purchases
2. Implement order expiration for pending orders
3. Add order splitting functionality
4. Enhanced reporting for event-based order analytics
