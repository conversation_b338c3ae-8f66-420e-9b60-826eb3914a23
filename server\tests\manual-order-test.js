const { PrismaClient } = require("@prisma/client");
const OrderService = require("../services/orderService");

const prisma = new PrismaClient();

async function testEventBasedOrderLogic() {
  console.log("🧪 Testing Event-Based Order Creation Logic");
  console.log("=" .repeat(50));

  const orderService = OrderService.getInstance();

  try {
    // Test 1: Helper method - getEventIdFromItems
    console.log("\n📋 Test 1: getEventIdFromItems");
    
    // Mock cart items from same event
    const cartItems = [
      { tickettypes: { event_id: 1 } },
      { tickettypes: { event_id: 1 } }
    ];
    
    const eventId = await orderService.getEventIdFromItems(cartItems);
    console.log(`✅ Event ID extracted: ${eventId}`);

    // Test 2: Helper method - findUserEventOrder
    console.log("\n📋 Test 2: findUserEventOrder");
    
    // This will test against real database - you may need to adjust user/event IDs
    const testUserId = 1;
    const testEventId = 1;
    
    const existingPendingOrder = await orderService.findUserEventOrder(testUserId, testEventId, "pending");
    console.log(`✅ Existing pending order found: ${existingPendingOrder ? 'Yes' : 'No'}`);
    
    if (existingPendingOrder) {
      console.log(`   Order ID: ${existingPendingOrder.order_id}`);
      console.log(`   Order Items: ${existingPendingOrder.orderitems.length}`);
    }

    const existingCompletedOrder = await orderService.findUserEventOrder(testUserId, testEventId, "completed");
    console.log(`✅ Existing completed order found: ${existingCompletedOrder ? 'Yes' : 'No'}`);

    // Test 3: Conditional Logic Verification
    console.log("\n📋 Test 3: Conditional Logic Verification");
    
    console.log("🔍 Testing conditional logic scenarios:");
    console.log("   Case 1: No order exists for user-event → Should create new order");
    console.log("   Case 2: Pending order exists → Should add to existing order");
    console.log("   Case 3: Only completed orders exist → Should create new order");
    
    if (!existingPendingOrder && !existingCompletedOrder) {
      console.log("✅ Scenario: Case 1 - No orders exist");
    } else if (existingPendingOrder) {
      console.log("✅ Scenario: Case 2 - Pending order exists");
    } else if (existingCompletedOrder && !existingPendingOrder) {
      console.log("✅ Scenario: Case 3 - Only completed orders exist");
    }

    // Test 4: Validate database schema relationships
    console.log("\n📋 Test 4: Database Schema Validation");
    
    // Check if we can query the relationships correctly
    const sampleOrder = await prisma.orders.findFirst({
      include: {
        orderitems: {
          include: {
            tickettypes: {
              include: {
                events: true
              }
            }
          }
        }
      }
    });

    if (sampleOrder) {
      console.log("✅ Database relationships working correctly");
      console.log(`   Order ID: ${sampleOrder.order_id}`);
      console.log(`   Payment Status: ${sampleOrder.payment_status}`);
      console.log(`   Order Items: ${sampleOrder.orderitems.length}`);
      
      if (sampleOrder.orderitems.length > 0) {
        const firstItem = sampleOrder.orderitems[0];
        console.log(`   First Item Event ID: ${firstItem.tickettypes.event_id}`);
        console.log(`   Event Title: ${firstItem.tickettypes.events?.title || 'N/A'}`);
      }
    } else {
      console.log("⚠️  No orders found in database for relationship testing");
    }
    localStorage.clear();
    // Test 5: Fee Calculation Verification
    console.log("\n📋 Test 5: Fee Calculation Verification");
    
    const testAmount = 100.00;
    const organizerFees = testAmount * 0.05; // 5%
    const serviceFees = testAmount * 0.1; // 10%
    const totalFees = organizerFees + serviceFees;
    
    console.log(`✅ Fee calculation test:`);
    console.log(`   Base Amount: $${testAmount}`);
    console.log(`   Organizer Fees (5%): $${organizerFees}`);
    console.log(`   Service Fees (10%): $${serviceFees}`);
    console.log(`   Total Additional Fees: $${totalFees}`);
    console.log(`   Final Total: $${testAmount + totalFees}`);

    console.log("\n🎉 All tests completed successfully!");
    console.log("=" .repeat(50));

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error("Stack trace:", error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  testEventBasedOrderLogic();
}

module.exports = { testEventBasedOrderLogic };
