const { PrismaClient } = require("@prisma/client");
const OrderService = require("../services/orderService");

// Mock Prisma Client
jest.mock("@prisma/client", () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    orders: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    orderitems: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    tickettypes: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    cart: {
      findMany: jest.fn(),
    },
  })),
}));

describe("OrderService - Event-Based Order Creation", () => {
  let orderService;
  let mockPrisma;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Get the mocked prisma instance
    mockPrisma = new PrismaClient();
    
    // Get OrderService instance
    orderService = OrderService.getInstance();
  });

  describe("findUserEventOrder", () => {
    it("should find existing pending order for user-event combination", async () => {
      const mockOrder = {
        order_id: 1,
        user_id: 1,
        payment_status: "pending",
        orderitems: [
          {
            order_item_id: 1,
            ticket_type_id: 1,
            quantity: 2,
            tickettypes: { event_id: 1 },
          },
        ],
      };

      mockPrisma.orders.findFirst.mockResolvedValue(mockOrder);

      const result = await orderService.findUserEventOrder(1, 1, "pending");

      expect(result).toEqual(mockOrder);
      expect(mockPrisma.orders.findFirst).toHaveBeenCalledWith({
        where: {
          user_id: 1,
          payment_status: "pending",
          orderitems: {
            some: {
              tickettypes: {
                event_id: 1,
              },
            },
          },
        },
        include: {
          orderitems: {
            include: {
              tickettypes: true,
            },
          },
        },
      });
    });

    it("should return null when no order exists", async () => {
      mockPrisma.orders.findFirst.mockResolvedValue(null);

      const result = await orderService.findUserEventOrder(1, 1, "pending");

      expect(result).toBeNull();
    });
  });

  describe("getEventIdFromItems", () => {
    it("should extract event ID from cart items", async () => {
      const cartItems = [
        { tickettypes: { event_id: 1 } },
        { tickettypes: { event_id: 1 } },
      ];

      const result = await orderService.getEventIdFromItems(cartItems);

      expect(result).toBe(1);
    });

    it("should extract event ID from selected tickets", async () => {
      const selectedTickets = [
        { ticketTypeId: 1 },
        { ticketTypeId: 2 },
      ];

      mockPrisma.tickettypes.findMany.mockResolvedValue([
        { event_id: 1 },
        { event_id: 1 },
      ]);

      const result = await orderService.getEventIdFromItems(selectedTickets);

      expect(result).toBe(1);
      expect(mockPrisma.tickettypes.findMany).toHaveBeenCalledWith({
        where: {
          ticket_type_id: { in: [1, 2] },
        },
        select: {
          event_id: true,
        },
      });
    });

    it("should throw error when items are from multiple events", async () => {
      const cartItems = [
        { tickettypes: { event_id: 1 } },
        { tickettypes: { event_id: 2 } },
      ];

      await expect(orderService.getEventIdFromItems(cartItems)).rejects.toThrow(
        "Items must be from the same event"
      );
    });
  });

  describe("createOrderFromCart - Event-Based Logic", () => {
    const mockCartItems = [
      {
        ticket_type_id: 1,
        quantity: 2,
        tickettypes: { event_id: 1, price: "50.00" },
      },
      {
        ticket_type_id: 2,
        quantity: 1,
        tickettypes: { event_id: 1, price: "75.00" },
      },
    ];

    it("should create new order when no pending order exists (Case 1)", async () => {
      // Mock no existing pending order
      mockPrisma.orders.findFirst.mockResolvedValue(null);

      // Mock order creation
      const mockOrder = {
        order_id: 1,
        user_id: 1,
        total_amount: 175.0,
        additional_fees: 26.25,
        payment_status: "pending",
      };
      mockPrisma.orders.create.mockResolvedValue(mockOrder);

      // Mock order items creation
      const mockOrderItems = [
        { order_item_id: 1, order_id: 1, ticket_type_id: 1, quantity: 2 },
        { order_item_id: 2, order_id: 1, ticket_type_id: 2, quantity: 1 },
      ];
      mockPrisma.orderitems.create
        .mockResolvedValueOnce(mockOrderItems[0])
        .mockResolvedValueOnce(mockOrderItems[1]);

      const result = await orderService.createOrderFromCart(1, mockCartItems);

      expect(result.order).toEqual(mockOrder);
      expect(result.orderItems).toEqual(mockOrderItems);
      expect(result.isExistingOrder).toBe(false);
      expect(mockPrisma.orders.create).toHaveBeenCalledWith({
        data: {
          user_id: 1,
          total_amount: 175.0,
          additional_fees: 26.25, // 5% + 10% of 175
          payment_status: "pending",
          payment_method: null,
          transaction_id: null,
        },
      });
    });

    it("should add to existing pending order (Case 2)", async () => {
      // Mock existing pending order
      const mockExistingOrder = {
        order_id: 1,
        user_id: 1,
        payment_status: "pending",
        orderitems: [
          {
            order_item_id: 1,
            ticket_type_id: 1,
            quantity: 1,
            tickettypes: { price: "50.00" },
          },
        ],
      };
      mockPrisma.orders.findFirst.mockResolvedValue(mockExistingOrder);

      // Mock order item update
      const mockUpdatedOrderItem = {
        order_item_id: 1,
        quantity: 3, // 1 existing + 2 new
      };
      mockPrisma.orderitems.update.mockResolvedValue(mockUpdatedOrderItem);

      // Mock new order item creation
      const mockNewOrderItem = {
        order_item_id: 2,
        order_id: 1,
        ticket_type_id: 2,
        quantity: 1,
      };
      mockPrisma.orderitems.create.mockResolvedValue(mockNewOrderItem);

      // Mock order items fetch for total calculation
      mockPrisma.orderitems.findMany.mockResolvedValue([
        { tickettypes: { price: "50.00" }, quantity: 3 },
        { tickettypes: { price: "75.00" }, quantity: 1 },
      ]);

      // Mock order update
      const mockUpdatedOrder = {
        order_id: 1,
        total_amount: 225.0,
        additional_fees: 33.75,
      };
      mockPrisma.orders.update.mockResolvedValue(mockUpdatedOrder);

      const result = await orderService.createOrderFromCart(1, mockCartItems);

      expect(result.order).toEqual(mockUpdatedOrder);
      expect(result.isExistingOrder).toBe(true);
      expect(mockPrisma.orderitems.update).toHaveBeenCalled();
      expect(mockPrisma.orderitems.create).toHaveBeenCalled();
      expect(mockPrisma.orders.update).toHaveBeenCalledWith({
        where: { order_id: 1 },
        data: {
          total_amount: 225.0,
          additional_fees: 33.75,
        },
      });
    });

    it("should create new order when completed order exists (Case 3)", async () => {
      // Mock no pending order (completed order exists but we don't find it)
      mockPrisma.orders.findFirst.mockResolvedValue(null);

      // Mock order creation (same as Case 1)
      const mockOrder = {
        order_id: 2,
        user_id: 1,
        total_amount: 175.0,
        additional_fees: 26.25,
        payment_status: "pending",
      };
      mockPrisma.orders.create.mockResolvedValue(mockOrder);

      const mockOrderItems = [
        { order_item_id: 3, order_id: 2, ticket_type_id: 1, quantity: 2 },
        { order_item_id: 4, order_id: 2, ticket_type_id: 2, quantity: 1 },
      ];
      mockPrisma.orderitems.create
        .mockResolvedValueOnce(mockOrderItems[0])
        .mockResolvedValueOnce(mockOrderItems[1]);

      const result = await orderService.createOrderFromCart(1, mockCartItems);

      expect(result.order).toEqual(mockOrder);
      expect(result.orderItems).toEqual(mockOrderItems);
      expect(result.isExistingOrder).toBe(false);
    });
  });

  describe("createOrderFromTickets - Event-Based Logic", () => {
    const mockSelectedTickets = [
      { ticketTypeId: 1, quantity: 2, price: "50.00" },
      { ticketTypeId: 2, quantity: 1, price: "75.00" },
    ];
    const mockAttendeeInfo = [
      { name: "John Doe", email: "<EMAIL>" },
      { name: "Jane Doe", email: "<EMAIL>" },
    ];

    it("should create new order when no pending order exists", async () => {
      // Mock ticket types lookup
      mockPrisma.tickettypes.findMany.mockResolvedValue([
        { event_id: 1 },
        { event_id: 1 },
      ]);

      // Mock no existing pending order
      mockPrisma.orders.findFirst.mockResolvedValue(null);

      // Mock order creation
      const mockOrder = {
        order_id: 1,
        user_id: 1,
        total_amount: 175.0,
        additional_fees: 26.25,
        payment_status: "pending",
      };
      mockPrisma.orders.create.mockResolvedValue(mockOrder);

      const mockOrderItems = [
        { order_item_id: 1, order_id: 1, ticket_type_id: 1, quantity: 2 },
        { order_item_id: 2, order_id: 1, ticket_type_id: 2, quantity: 1 },
      ];
      mockPrisma.orderitems.create
        .mockResolvedValueOnce(mockOrderItems[0])
        .mockResolvedValueOnce(mockOrderItems[1]);

      const result = await orderService.createOrderFromTickets(
        1,
        mockSelectedTickets,
        mockAttendeeInfo
      );

      expect(result.order).toEqual(mockOrder);
      expect(result.orderItems).toEqual(mockOrderItems);
      expect(result.ticketsWithAttendeeInfo).toEqual(mockAttendeeInfo);
      expect(result.isExistingOrder).toBe(false);
    });
  });

  describe("addToCart - Event-Based Logic", () => {
    const mockTicketType = {
      ticket_type_id: 1,
      event_id: 1,
      price: "50.00",
      quantity_available: 100,
      max_per_order: 5,
    };

    it("should create new order when no pending order exists", async () => {
      mockPrisma.tickettypes.findUnique.mockResolvedValue(mockTicketType);
      mockPrisma.orders.findFirst.mockResolvedValue(null);

      const mockOrder = {
        order_id: 1,
        user_id: 1,
        total_amount: 100.0,
        additional_fees: 15.0,
        payment_status: "pending",
      };
      mockPrisma.orders.create.mockResolvedValue(mockOrder);

      const mockOrderItem = {
        order_item_id: 1,
        order_id: 1,
        ticket_type_id: 1,
        quantity: 2,
      };
      mockPrisma.orderitems.create.mockResolvedValue(mockOrderItem);

      const result = await orderService.addToCart(1, 1, 2);

      expect(result).toEqual(mockOrderItem);
      expect(mockPrisma.orders.create).toHaveBeenCalled();
      expect(mockPrisma.orderitems.create).toHaveBeenCalled();
    });

    it("should add to existing pending order", async () => {
      mockPrisma.tickettypes.findUnique.mockResolvedValue(mockTicketType);

      const mockExistingOrder = {
        order_id: 1,
        orderitems: [],
      };
      mockPrisma.orders.findFirst.mockResolvedValue(mockExistingOrder);

      const mockOrderItem = {
        order_item_id: 2,
        order_id: 1,
        ticket_type_id: 1,
        quantity: 2,
      };
      mockPrisma.orderitems.create.mockResolvedValue(mockOrderItem);

      // Mock order total update
      mockPrisma.orderitems.findMany.mockResolvedValue([
        { tickettypes: { price: "50.00" }, quantity: 2 },
      ]);
      mockPrisma.orders.update.mockResolvedValue({});

      const result = await orderService.addToCart(1, 1, 2);

      expect(result).toEqual(mockOrderItem);
      expect(mockPrisma.orderitems.create).toHaveBeenCalled();
      expect(mockPrisma.orders.update).toHaveBeenCalled();
    });
  });
});
